<template>
  <div class="card-carousel-container"
       @touchstart="handleTouchStart"
       @touchmove="handleTouchMove"
       @touchend="handleTouchEnd">
    <div
      class="card"
      v-for="(card, index) in cards"
      :key="index"
      :style="getCardStyle(index)"
    >
      <div class="card-content">
        <h3>{{ card.title }}</h3>
        <p v-if="index === selectedIndex">{{ card.description }}</p>
        <button
          v-if="index === selectedIndex"
          class="learn-more-btn"
          @click="navigateToDetail(index + 1)"
        >
          点击了解更多
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue';
import { useRouter } from 'vue-router';

const router = useRouter();

const cards = ref([
  { title: '校园环境', description: '龙子湖校区、文化路校区、许昌校区' },
  { title: '卡片 2', description: '这是第二张卡片的内容，同样非常详细。' },
  { title: '卡片 3', description: '这是第三张卡片的内容，同样非常详细。' },
  { title: '卡片 4', description: '这是第四张卡片的内容，同样非常详细。' },
  { title: '卡片 5', description: '这是第五张卡片的内容，同样非常详细。' },
  { title: '卡片 6', description: '这是第六张卡片的内容，同样非常详细。' },
  { title: '卡片 7', description: '这是第七张卡片的内容，同样非常详细。' },
  { title: '卡片 8', description: '这是第八张卡片的内容，同样非常详细。' },
  { title: '卡片 9', description: '这是第九张卡片的内容，同样非常详细。' },
  { title: '卡片 10', description: '这是第十张卡片的内容，同样非常详细。' },
]);

// 当前选中的卡片索引
const selectedIndex = ref(3);

// 手势相关变量
const startX = ref(0);
const currentX = ref(0);
const startTime = ref(0); // 新增：记录触摸开始时间
const isSwiping = ref(false);

// 扇形布局参数
const fanRadius = 200; // 扇形半径
const fanAngleSpread = 60; // 扇形角度范围（度）
const maxVisibleCards = 5; // 最大可见卡片数

// 计算每个卡片的动态样式（扇形布局）
const getCardStyle = computed(() => (index) => {
  const diff = index - selectedIndex.value;
  const absDiff = Math.abs(diff);

  let offset = 0;
  if (isSwiping.value) {
    offset = currentX.value - startX.value;
  }

  let style = {
    opacity: 0,
    zIndex: 1,
    transform: `translate(0px, 0px) scale(1) rotate(0deg)`,
    transition: 'all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94)',
    transformOrigin: 'center bottom', // 设置旋转中心点
  };

  if (index === selectedIndex.value) {
    // 当前选中的卡片：位于中心，完全展开
    style.opacity = 1;
    style.zIndex = 100;
    style.transform = `translateX(${offset * 0.5}px) translateY(0px) scale(1) rotate(0deg)`;
  } else if (absDiff <= maxVisibleCards) {
    // 可见范围内的卡片：扇形排列
    const side = diff > 0 ? 1 : -1; // 右侧为正，左侧为负
    const position = Math.min(absDiff, maxVisibleCards);

    // 计算扇形位置
    const angle = (side * position * fanAngleSpread) / maxVisibleCards; // 角度
    const distance = fanRadius * (position / maxVisibleCards); // 距离

    // 计算位置
    const x = Math.sin(angle * Math.PI / 180) * distance;
    const y = Math.cos(angle * Math.PI / 180) * distance - distance * 0.3; // 稍微向上偏移

    // 计算缩放和透明度
    const scale = Math.max(0.6, 1 - position * 0.15);
    const opacity = Math.max(0.3, 1 - position * 0.2);

    // 应用滑动偏移
    const swipeInfluence = offset * 0.3; // 滑动影响系数

    style.opacity = opacity;
    style.zIndex = 50 - position;
    style.transform = `translateX(${x + swipeInfluence}px) translateY(${y}px) scale(${scale}) rotate(${angle * 0.5}deg)`;
  } else {
    // 超出可见范围的卡片：完全隐藏
    style.opacity = 0;
    style.zIndex = 1;
  }

  return style;
});

// 处理手势开始
const handleTouchStart = (e) => {
  isSwiping.value = true;
  startX.value = e.touches[0].clientX;
  currentX.value = startX.value;
  startTime.value = Date.now(); // 记录开始时间
};

// 处理手势移动
const handleTouchMove = (e) => {
  if (!isSwiping.value) return;
  currentX.value = e.touches[0].clientX;
};

// 处理手势结束
const handleTouchEnd = () => {
  if (!isSwiping.value) return;
  isSwiping.value = false;

  const moveDistance = currentX.value - startX.value;
  const moveDuration = Date.now() - startTime.value;

  // 计算滑动速度：距离 / 时间 (像素/毫秒)
  const moveVelocity = Math.abs(moveDistance) / moveDuration;

  // 优化后的阈值设置
  const minSwipeDistance = 30; // 降低最小滑动距离，更容易触发
  const minSwipeVelocity = 0.3; // 降低最小滑动速度
  const fastSwipeVelocity = 1.0; // 快速滑动的速度阈值

  // 判断滑动方向和强度
  if (Math.abs(moveDistance) < minSwipeDistance && moveVelocity < minSwipeVelocity) {
    // 滑动距离和速度都不够，不切换
    return;
  }

  // 向右滑动 - 返回上一张卡片
  if (moveDistance > 0) {
    if (moveDistance > minSwipeDistance || moveVelocity > minSwipeVelocity) {
      // 快速滑动可以跳过多张
      if (moveVelocity > fastSwipeVelocity && moveDistance > 80) {
        const cardsToMove = Math.min(Math.ceil(moveDistance / 100), 3); // 最多跳3张
        selectedIndex.value = Math.max(0, selectedIndex.value - cardsToMove);
      } else {
        // 普通滑动，返回一张
        selectedIndex.value = Math.max(0, selectedIndex.value - 1);
      }
    }
  }
  // 向左滑动 - 前进到下一张卡片
  else if (moveDistance < 0) {
    if (Math.abs(moveDistance) > minSwipeDistance || moveVelocity > minSwipeVelocity) {
      // 快速滑动可以跳过多张
      if (moveVelocity > fastSwipeVelocity && Math.abs(moveDistance) > 80) {
        const cardsToMove = Math.min(Math.ceil(Math.abs(moveDistance) / 100), 3); // 最多跳3张
        selectedIndex.value = Math.min(cards.value.length - 1, selectedIndex.value + cardsToMove);
      } else {
        // 普通滑动，前进一张
        selectedIndex.value = Math.min(cards.value.length - 1, selectedIndex.value + 1);
      }
    }
  }
};

// 导航到详情页面
const navigateToDetail = (cardNumber) => {
  router.push(`/detail/${cardNumber}`);
};
</script>

<style scoped>
.card-carousel-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  margin: 0;
  padding: 0;
  overflow: hidden;
  display: flex;
  justify-content: center;
  align-items: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  /* 移除边框圆角和阴影，因为是全屏 */
}

.card {
  position: absolute;
  width: 90%;
  max-width: 400px;
  min-height: 300px;
  height: auto;
  background-color: #fff;
  border-radius: 20px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  cursor: grab;
  touch-action: pan-x;
  /* 添加一些移动端优化 */
  -webkit-tap-highlight-color: transparent;
  user-select: none;
}

.card-content {
  padding: 30px 25px;
  text-align: center;
  color: #333;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

h3 {
  margin: 0 0 20px 0;
  font-size: 1.8em;
  font-weight: 600;
  color: #2c3e50;
}

p {
  font-size: 1.1em;
  line-height: 1.6;
  color: #666;
  margin: 0 0 20px 0;
}

.learn-more-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 25px;
  font-size: 1em;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
  -webkit-tap-highlight-color: transparent;
}

.learn-more-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
}

.learn-more-btn:active {
  transform: translateY(0px);
  box-shadow: 0 2px 10px rgba(102, 126, 234, 0.3);
}

/* 移动端全屏优化 */
@media (max-width: 768px) {
  .card {
    width: 95%;
    min-height: 350px;
  }

  .card-content {
    padding: 40px 30px;
  }

  h3 {
    font-size: 2em;
  }

  p {
    font-size: 1.2em;
  }
}

/* 防止页面滚动 */
body {
  margin: 0;
  padding: 0;
  overflow: hidden;
}
</style>