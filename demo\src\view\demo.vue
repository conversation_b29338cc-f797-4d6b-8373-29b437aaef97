<template>
  <div class="card-carousel-container"
       @touchstart="handleTouchStart"
       @touchmove="handleTouchMove"
       @touchend="handleTouchEnd">
    <div
      class="card"
      v-for="(card, index) in cards"
      :key="index"
      :style="getCardStyle(index)"
    >
      <div class="card-content">
        <h3>{{ card.title }}</h3>
        <p v-if="index === selectedIndex">{{ card.description }}</p>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue';

const cards = ref([
  { title: '卡片 1', description: '这是第一张卡片的内容，非常详细。' },
  { title: '卡片 2', description: '这是第二张卡片的内容，同样非常详细。' },
  { title: '卡片 3', description: '这是第三张卡片的内容，同样非常详细。' },
  { title: '卡片 4', description: '这是第四张卡片的内容，同样非常详细。' },
  { title: '卡片 5', description: '这是第五张卡片的内容，同样非常详细。' },
  { title: '卡片 6', description: '这是第六张卡片的内容，同样非常详细。' },
  { title: '卡片 7', description: '这是第七张卡片的内容，同样非常详细。' },
]);

// 当前选中的卡片索引
const selectedIndex = ref(3);

// 手势相关变量
const startY = ref(0);
const currentY = ref(0);
const startTime = ref(0); // 新增：记录触摸开始时间
const isSwiping = ref(false);

// 卡片之间的垂直间距，可调整
const cardSpacing = 60;

// 计算每个卡片的动态样式
const getCardStyle = computed(() => (index) => {
  const diff = index - selectedIndex.value;

  let offset = 0;
  if (isSwiping.value) {
    offset = currentY.value - startY.value;
  }

  let style = {
    opacity: 0,
    zIndex: 1,
    transform: `translateY(0px) scale(1)`,
    transition: 'all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94)',
  };

  if (index === selectedIndex.value) {
    // 当前选中的卡片：完全展开，位于中心
    style.opacity = 1;
    style.zIndex = 100;
    style.transform = `translateY(${offset}px) scale(1)`;
  } else if (diff > 0) {
    // 下方的卡片：堆叠显示
    const stackIndex = diff - 1;
    const stackOffset = Math.min(stackIndex * 15, 60); // 最大堆叠偏移60px
    const stackScale = Math.max(0.85 - stackIndex * 0.05, 0.7); // 最小缩放0.7
    const stackOpacity = Math.max(0.6 - stackIndex * 0.1, 0.3); // 最小透明度0.3

    style.opacity = stackOpacity;
    style.zIndex = 50 - stackIndex;
    style.transform = `translateY(${cardSpacing + stackOffset + offset}px) scale(${stackScale})`;
  } else {
    // 上方的卡片：展示过的卡片，随滑动逐渐消失
    const absDiff = Math.abs(diff);
    const fadeDistance = 100; // 消失距离
    const currentOffset = Math.abs(offset);

    // 基础位置和样式
    let baseOpacity = Math.max(0.4 - (absDiff - 1) * 0.1, 0.1);
    let baseScale = Math.max(0.8 - (absDiff - 1) * 0.05, 0.6);

    // 根据滑动距离调整透明度（向上滑动时逐渐消失）
    if (offset > 0) {
      // 向上滑动，展示过的卡片逐渐消失
      const fadeRatio = Math.min(currentOffset / fadeDistance, 1);
      baseOpacity = baseOpacity * (1 - fadeRatio);
    }

    style.opacity = baseOpacity;
    style.zIndex = 30 - absDiff;
    style.transform = `translateY(${diff * cardSpacing + offset}px) scale(${baseScale})`;
  }

  return style;
});

// 处理手势开始
const handleTouchStart = (e) => {
  isSwiping.value = true;
  startY.value = e.touches[0].clientY;
  currentY.value = startY.value;
  startTime.value = Date.now(); // 记录开始时间
};

// 处理手势移动
const handleTouchMove = (e) => {
  if (!isSwiping.value) return;
  currentY.value = e.touches[0].clientY;
};

// 处理手势结束
const handleTouchEnd = () => {
  if (!isSwiping.value) return;
  isSwiping.value = false;

  const moveDistance = currentY.value - startY.value;
  const moveDuration = Date.now() - startTime.value;
  
  // 计算滑动速度：距离 / 时间 (像素/毫秒)
  const moveVelocity = Math.abs(moveDistance) / moveDuration;

  // 根据滑动距离和速度来决定切换的卡片数量
  let cardsToMove = 0;
  const minSwipeDistance = 50; // 最小滑动距离
  const minSwipeVelocity = 0.5; // 最小滑动速度 (例如：0.5px/ms)

  if (moveDistance > minSwipeDistance && moveVelocity > minSwipeVelocity) {
    // 根据滑动距离和速度计算切换的卡片数量，可以根据你的需求调整这个公式
    cardsToMove = Math.ceil(Math.abs(moveDistance) / 150);
    selectedIndex.value = Math.max(0, selectedIndex.value - cardsToMove);
  } else if (moveDistance < -minSwipeDistance && moveVelocity > minSwipeVelocity) {
    cardsToMove = Math.ceil(Math.abs(moveDistance) / 150);
    selectedIndex.value = Math.min(cards.value.length - 1, selectedIndex.value + cardsToMove);
  } else {
    // 如果滑动距离或速度不够，只切换一个，或者不切换，取决于你的阈值
    if (moveDistance > 50) {
      selectedIndex.value = Math.max(0, selectedIndex.value - 1);
    } else if (moveDistance < -50) {
      selectedIndex.value = Math.min(cards.value.length - 1, selectedIndex.value + 1);
    }
  }
};
</script>

<style scoped>
.card-carousel-container {
  position: relative;
  width: 90%;
  height: 500px;
  margin: 0 auto;
  overflow: hidden;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #f0f2f5;
  border-radius: 10px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.card {
  position: absolute;
  width: 80%;
  min-height: 150px;
  background-color: #fff;
  border-radius: 10px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  cursor: grab;
  touch-action: pan-y;
}

.card-content {
  padding: 20px;
  text-align: center;
  color: #333;
}

h3 {
  margin-top: 0;
  font-size: 1.2em;
}

p {
  font-size: 0.9em;
  color: #666;
}
</style>