import { createRouter, createWebHistory } from 'vue-router'
import Demo from '../view/demo.vue'
import Detail from '../view/detail.vue'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      name: 'home',
      component: Demo
    },
    {
      path: '/detail/:id',
      name: 'detail',
      component: Detail,
      props: true
    }
  ],
})

export default router
