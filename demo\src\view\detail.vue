<template>
  <div class="detail-container">
    <div class="detail-header">
      <button class="back-btn" @click="goBack">
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M19 12H5M12 19L5 12L12 5" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
        返回
      </button>
    </div>
    
    <div class="detail-content">
      <div class="detail-card">
        <h1>{{ getCardTitle(cardId) }}</h1>
        <div class="welcome-message">
          <h2>欢迎来到卡片{{ cardId }}</h2>
          <p>这里是卡片{{ cardId }}的详细页面内容。</p>
          <div class="content-placeholder">
            <div class="placeholder-item">
              <h3>功能介绍</h3>
              <p>这里可以放置卡片{{ cardId }}的详细功能介绍和说明。</p>
            </div>
            <div class="placeholder-item">
              <h3>使用指南</h3>
              <p>这里可以放置卡片{{ cardId }}的使用方法和操作指南。</p>
            </div>
            <div class="placeholder-item">
              <h3>更多信息</h3>
              <p>这里可以放置卡片{{ cardId }}的更多相关信息和资源链接。</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue';
import { useRoute, useRouter } from 'vue-router';

const route = useRoute();
const router = useRouter();

// 获取路由参数中的卡片ID
const cardId = computed(() => route.params.id);

// 卡片标题映射
const cardTitles = {
  1: '卡片 1',
  2: '卡片 2', 
  3: '卡片 3',
  4: '卡片 4',
  5: '卡片 5',
  6: '卡片 6',
  7: '卡片 7',
  8: '卡片 8',
  9: '卡片 9',
  10: '卡片 10'
};

// 获取卡片标题
const getCardTitle = (id) => {
  return cardTitles[id] || `卡片 ${id}`;
};

// 返回上一页
const goBack = () => {
  router.back();
};
</script>

<style scoped>
.detail-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  overflow-y: auto;
  padding: 0;
  margin: 0;
}

.detail-header {
  position: sticky;
  top: 0;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  padding: 20px;
  z-index: 100;
}

.back-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: none;
  padding: 12px 20px;
  border-radius: 25px;
  font-size: 1em;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  -webkit-tap-highlight-color: transparent;
}

.back-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-2px);
}

.back-btn:active {
  transform: translateY(0px);
}

.detail-content {
  padding: 20px;
  display: flex;
  justify-content: center;
  align-items: flex-start;
  min-height: calc(100vh - 80px);
}

.detail-card {
  background: white;
  border-radius: 20px;
  padding: 40px;
  max-width: 600px;
  width: 100%;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
  margin-bottom: 40px;
}

.detail-card h1 {
  color: #2c3e50;
  font-size: 2.5em;
  margin: 0 0 20px 0;
  text-align: center;
}

.welcome-message h2 {
  color: #667eea;
  font-size: 2em;
  margin: 0 0 20px 0;
  text-align: center;
}

.welcome-message > p {
  color: #666;
  font-size: 1.2em;
  text-align: center;
  margin-bottom: 40px;
}

.content-placeholder {
  display: flex;
  flex-direction: column;
  gap: 30px;
}

.placeholder-item {
  background: #f8f9fa;
  padding: 25px;
  border-radius: 15px;
  border-left: 4px solid #667eea;
}

.placeholder-item h3 {
  color: #2c3e50;
  font-size: 1.4em;
  margin: 0 0 15px 0;
}

.placeholder-item p {
  color: #666;
  font-size: 1em;
  line-height: 1.6;
  margin: 0;
}

/* 移动端优化 */
@media (max-width: 768px) {
  .detail-card {
    padding: 30px 20px;
    margin: 0 10px 20px 10px;
  }
  
  .detail-card h1 {
    font-size: 2em;
  }
  
  .welcome-message h2 {
    font-size: 1.6em;
  }
  
  .placeholder-item {
    padding: 20px;
  }
}

/* 防止页面滚动问题 */
body {
  margin: 0;
  padding: 0;
}
</style>
