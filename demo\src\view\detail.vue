<template>
  <div class="detail-container">
    <div class="detail-header">
      <button class="back-btn" @click="goBack">
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M19 12H5M12 19L5 12L12 5" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
        返回
      </button>
    </div>
    
    <div class="detail-content">
      <div class="detail-card">
        <h1>{{ getCardTitle(cardId) }}</h1>

        <!-- 卡片1的特殊内容 -->
        <div v-if="cardId == 1" class="school-introduction">
          <div class="section">
            <h2>一、学校介绍</h2>

            <div class="subsection">
              <h3>（一）学校概况</h3>
              <p>学校源自于 1902 年创办的河南大学堂，是农业农村部、国家林业和草原局与河南省人民政府共建高校、国家首批"2011 计划"建设高校、全国深化创新创业教育改革示范高校、全国毕业生就业典型经验高校 50 强、全国创新创业典型经验高校 50 强、河南省特色骨干大学建设高校、河南省"双一流"创建工程高校。</p>

              <p>2023 年，省政府常务会议研究支持河南农业大学创建中国特色世界一流农业大学，印发《河南省人民政府办公厅关于支持河南农业大学创建中国特色世界一流农业大学的若干意见》；获批建设中国现代农业联合研究生院，与中国农业科学院研究生院一体化发展。</p>

              <p>学校目前有郑州文化路校区（即老区，包括文化路校区和李园校区）、龙子湖校区（即新区）、以及许昌校区。（各专业、学院校区分布请参考【四、住宿相关】。）</p>

              <p>学校现有 1 个一级学科国家级重点学科，2 个河南省"双一流"创建学科，4 个河南省特色骨干学科群，1 个河南省特需急需特色骨干学科群，17 个省部级重点学科，7 个 ESI 全球前 1%学科。9 个博士后科研流动站，10 个一级学科博士学位授权点，2 个博士专业学位授权点。19 个一级学科硕士学位授权点，20 个硕士专业学位授权类别。89 个本科专业，其中国家级一流本科专业建设点 19 个，河南省一流本科专业建设点 23 个。</p>

              <p>学校现有中国工程院院士 5 人：张改平、周卫、康相涛、张新友、许为刚。</p>
            </div>

            <div class="subsection">
              <h3>（二）学校地理位置</h3>
              <div class="campus-info">
                <div class="campus-item">
                  <h4>1. 文化路校区</h4>
                  <p>位于河南省郑州市金水区农业路 63 号，靠近郑州市中心，生活便利。</p>
                </div>
                <div class="campus-item">
                  <h4>2. 龙子湖校区</h4>
                  <p>位于河南省郑州市郑东新区平安大道 218 号，靠近龙子湖智慧岛，地铁站距校门口约 200 米。</p>
                </div>
                <div class="campus-item">
                  <h4>3. 许昌校区</h4>
                  <p>位于许昌市建安区苏桥镇永宁街与劳动北路交叉口北 500 米，环境优美。</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 其他卡片的默认内容 -->
        <div v-else class="welcome-message">
          <h2>欢迎来到卡片{{ cardId }}</h2>
          <p>这里是卡片{{ cardId }}的详细页面内容。</p>
          <div class="content-placeholder">
            <div class="placeholder-item">
              <h3>功能介绍</h3>
              <p>这里可以放置卡片{{ cardId }}的详细功能介绍和说明。</p>
            </div>
            <div class="placeholder-item">
              <h3>使用指南</h3>
              <p>这里可以放置卡片{{ cardId }}的使用方法和操作指南。</p>
            </div>
            <div class="placeholder-item">
              <h3>更多信息</h3>
              <p>这里可以放置卡片{{ cardId }}的更多相关信息和资源链接。</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue';
import { useRoute, useRouter } from 'vue-router';

const route = useRoute();
const router = useRouter();

// 获取路由参数中的卡片ID
const cardId = computed(() => route.params.id);

// 卡片标题映射
const cardTitles = {
  1: '校园环境',
  2: '卡片 2',
  3: '卡片 3',
  4: '卡片 4',
  5: '卡片 5',
  6: '卡片 6',
  7: '卡片 7',
  8: '卡片 8',
  9: '卡片 9',
  10: '卡片 10'
};

// 获取卡片标题
const getCardTitle = (id) => {
  return cardTitles[id] || `卡片 ${id}`;
};

// 返回上一页
const goBack = () => {
  router.back();
};
</script>

<style scoped>
.detail-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  overflow-y: auto;
  padding: 0;
  margin: 0;
}

.detail-header {
  position: sticky;
  top: 0;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  padding: 20px;
  z-index: 100;
}

.back-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: none;
  padding: 12px 20px;
  border-radius: 25px;
  font-size: 1em;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  -webkit-tap-highlight-color: transparent;
}

.back-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-2px);
}

.back-btn:active {
  transform: translateY(0px);
}

.detail-content {
  padding: 20px;
  display: flex;
  justify-content: center;
  align-items: flex-start;
  min-height: calc(100vh - 80px);
}

.detail-card {
  background: white;
  border-radius: 20px;
  padding: 40px;
  max-width: 600px;
  width: 100%;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
  margin-bottom: 40px;
}

.detail-card h1 {
  color: #2c3e50;
  font-size: 2.5em;
  margin: 0 0 20px 0;
  text-align: center;
}

.welcome-message h2 {
  color: #667eea;
  font-size: 2em;
  margin: 0 0 20px 0;
  text-align: center;
}

.welcome-message > p {
  color: #666;
  font-size: 1.2em;
  text-align: center;
  margin-bottom: 40px;
}

.content-placeholder {
  display: flex;
  flex-direction: column;
  gap: 30px;
}

.placeholder-item {
  background: #f8f9fa;
  padding: 25px;
  border-radius: 15px;
  border-left: 4px solid #667eea;
}

.placeholder-item h3 {
  color: #2c3e50;
  font-size: 1.4em;
  margin: 0 0 15px 0;
}

.placeholder-item p {
  color: #666;
  font-size: 1em;
  line-height: 1.6;
  margin: 0;
}

/* 移动端优化 */
@media (max-width: 768px) {
  .detail-card {
    padding: 30px 20px;
    margin: 0 10px 20px 10px;
  }
  
  .detail-card h1 {
    font-size: 2em;
  }
  
  .welcome-message h2 {
    font-size: 1.6em;
  }
  
  .placeholder-item {
    padding: 20px;
  }
}

/* 学校介绍专用样式 */
.school-introduction {
  text-align: left;
}

.section {
  margin-bottom: 30px;
}

.section h2 {
  color: #2c3e50;
  font-size: 1.8em;
  margin: 0 0 25px 0;
  padding-bottom: 10px;
  border-bottom: 3px solid #667eea;
}

.subsection {
  margin-bottom: 25px;
}

.subsection h3 {
  color: #667eea;
  font-size: 1.4em;
  margin: 0 0 15px 0;
  font-weight: 600;
}

.subsection p {
  color: #444;
  font-size: 1em;
  line-height: 1.8;
  margin-bottom: 15px;
  text-align: justify;
}

.campus-info {
  display: flex;
  flex-direction: column;
  gap: 20px;
  margin-top: 20px;
}

.campus-item {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 12px;
  border-left: 4px solid #667eea;
  transition: all 0.3s ease;
}

.campus-item:hover {
  background: #e8f4fd;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.1);
}

.campus-item h4 {
  color: #2c3e50;
  font-size: 1.2em;
  margin: 0 0 10px 0;
  font-weight: 600;
}

.campus-item p {
  color: #666;
  font-size: 1em;
  line-height: 1.6;
  margin: 0;
}

/* 防止页面滚动问题 */
body {
  margin: 0;
  padding: 0;
}
</style>
